import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
    FileText,
    Globe,
    Database,
    Settings,
    Plus,
    Upload,
    Link,
    Zap,
    CheckCircle,
    AlertCircle,
    Clock,
    ArrowRight,
    BookOpen,
    Brain,
    Sparkles
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/use-auth'
import { ModernProjectDialog } from '@/components/knowledge-base/modern-project-dialog'
import { ModernDocumentManager } from '@/components/knowledge-base/modern-document-manager'
import { ModernSourceManager } from '@/components/knowledge-base/modern-source-manager'
import { ModernSettingsManager } from '@/components/knowledge-base/modern-settings-manager'

// Types for the new interface
interface Project {
    id: number
    name: string
    description?: string
    created_at: string
    documents_count?: number
    sources_count?: number
    status?: string
}

interface KnowledgeStats {
    total_documents: number
    total_sources: number
    active_sources: number
    embeddings_generated: number
    last_updated: string
}

interface QuickAction {
    id: string
    title: string
    description: string
    icon: React.ReactNode
    action: () => void
    status?: 'available' | 'processing' | 'completed'
}

export default function KnowledgeBaseModule() {
    const { user } = useAuth()
    const [projects, setProjects] = useState<Project[]>([])
    const [selectedProject, setSelectedProject] = useState<Project | null>(null)
    const [stats, setStats] = useState<KnowledgeStats | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [activeView, setActiveView] = useState<'overview' | 'documents' | 'sources' | 'settings'>('overview')
    const [showCreateProject, setShowCreateProject] = useState(false)

    // Fetch projects and stats
    useEffect(() => {
        const fetchData = async () => {
            if (!user) return

            setIsLoading(true)
            try {
                const response = await knowledgeBaseService.getProjects()
                if (response?.data?.success) {
                    const projectsData = response.data.data || []
                    setProjects(projectsData)

                    // Auto-select first project if available
                    if (projectsData.length > 0 && !selectedProject) {
                        setSelectedProject(projectsData[0])
                    }
                }
            } catch (error) {
                console.error('Failed to load projects:', error)
                toast.error('Failed to load knowledge base data')
            } finally {
                setIsLoading(false)
            }
        }

        fetchData()
    }, [user])

    // Fetch stats when project changes
    useEffect(() => {
        const fetchStats = async () => {
            if (!selectedProject) return

            try {
                // This would be a new API endpoint for getting project stats
                const response = await knowledgeBaseService.getProjectStats?.(selectedProject.id)
                if (response?.data?.success) {
                    setStats(response.data.data)
                } else {
                    // Provide default stats if API fails
                    setStats({
                        total_documents: 0,
                        total_sources: 0,
                        active_sources: 0,
                        embeddings_generated: 0,
                        last_updated: new Date().toISOString()
                    })
                }
            } catch (error) {
                console.error('Failed to load project stats:', error)
                // Provide default stats on error
                setStats({
                    total_documents: 0,
                    total_sources: 0,
                    active_sources: 0,
                    embeddings_generated: 0,
                    last_updated: new Date().toISOString()
                })
            }
        }

        fetchStats()
    }, [selectedProject])

    // Handle project creation
    const handleProjectCreated = (newProject: Project) => {
        setProjects(prev => [...prev, newProject])
        setSelectedProject(newProject)
        setShowCreateProject(false)
        toast.success(`Knowledge base "${newProject.name}" created successfully!`)
    }

    // Quick actions for the current view
    const getQuickActions = (): QuickAction[] => {
        if (!selectedProject) return []

        return [
            {
                id: 'upload-documents',
                title: 'Upload Documents',
                description: 'Add PDFs, Word docs, or text files',
                icon: <Upload className="h-5 w-5" />,
                action: () => setActiveView('documents'),
                status: 'available'
            },
            {
                id: 'scrape-website',
                title: 'Import from Website',
                description: 'Extract content from web pages',
                icon: <Link className="h-5 w-5" />,
                action: () => setActiveView('sources'),
                status: 'available'
            },
            {
                id: 'connect-database',
                title: 'Connect Database',
                description: 'Link to external data sources',
                icon: <Database className="h-5 w-5" />,
                action: () => setActiveView('sources'),
                status: 'available'
            },
            {
                id: 'generate-embeddings',
                title: 'Process Content',
                description: 'Make your content AI-searchable',
                icon: <Zap className="h-5 w-5" />,
                action: () => {
                    toast.info('Processing content...')
                },
                status: stats?.embeddings_generated ? 'completed' : 'available'
            }
        ]
    }

    if (!user) {
        return (
            <Card className="max-w-2xl mx-auto">
                <CardHeader className="text-center">
                    <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                        <BookOpen className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle>Knowledge Base</CardTitle>
                    <CardDescription>
                        Please log in to access your knowledge base
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    if (isLoading) {
        return (
            <Card className="max-w-4xl mx-auto">
                <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-muted-foreground">Loading your knowledge base...</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    if (projects.length === 0) {
        return (
            <div className="max-w-4xl mx-auto space-y-6">
                {/* Welcome Header */}
                <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                    <CardHeader className="text-center">
                        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center mb-4">
                            <Brain className="h-8 w-8 text-white" />
                        </div>
                        <CardTitle className="text-2xl">Welcome to AI Knowledge Base</CardTitle>
                        <CardDescription className="text-lg">
                            Teach your AI assistant with your own content and data
                        </CardDescription>
                    </CardHeader>
                </Card>

                {/* Getting Started */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Sparkles className="h-5 w-5 text-primary" />
                            Get Started in 3 Simple Steps
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid md:grid-cols-3 gap-6">
                            <div className="text-center p-6 rounded-lg border border-dashed border-primary/30 hover:border-primary/50 transition-colors">
                                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <Plus className="h-6 w-6 text-primary" />
                                </div>
                                <h3 className="font-semibold mb-2">1. Create Project</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Start by creating your first knowledge base project
                                </p>
                                <Button
                                    onClick={() => setShowCreateProject(true)}
                                    className="w-full"
                                >
                                    Create Project
                                </Button>
                            </div>

                            <div className="text-center p-6 rounded-lg border border-dashed border-muted-foreground/30">
                                <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                                    <Upload className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <h3 className="font-semibold mb-2 text-muted-foreground">2. Add Content</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Upload documents, connect websites, or link databases
                                </p>
                                <Button variant="outline" disabled className="w-full">
                                    Add Content
                                </Button>
                            </div>

                            <div className="text-center p-6 rounded-lg border border-dashed border-muted-foreground/30">
                                <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                                    <Zap className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <h3 className="font-semibold mb-2 text-muted-foreground">3. Activate AI</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Process your content to make it AI-searchable
                                </p>
                                <Button variant="outline" disabled className="w-full">
                                    Process Content
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="max-w-7xl mx-auto space-y-6">
            {/* Header */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/60 rounded-xl flex items-center justify-center">
                                <Brain className="h-6 w-6 text-white" />
                            </div>
                            <div>
                                <CardTitle className="text-2xl">AI Knowledge Base</CardTitle>
                                <CardDescription>
                                    Teach your AI assistant with your content
                                </CardDescription>
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            {selectedProject && (
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="px-3 py-1">
                                        {selectedProject.name}
                                    </Badge>
                                    {projects.length > 1 && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => {
                                                const currentIndex = projects.findIndex(p => p.id === selectedProject.id)
                                                const nextIndex = (currentIndex + 1) % projects.length
                                                setSelectedProject(projects[nextIndex])
                                            }}
                                        >
                                            Switch Project
                                        </Button>
                                    )}
                                </div>
                            )}
                            <Button
                                onClick={() => setShowCreateProject(true)}
                                className="bg-gradient-to-r from-primary to-primary/80"
                            >
                                <Plus className="h-4 w-4 mr-2" />
                                New Project
                            </Button>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Stats Overview */}
            {selectedProject && stats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <FileText className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold">{stats.total_documents}</p>
                                    <p className="text-sm text-muted-foreground">Documents</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <Database className="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold">{stats.active_sources}</p>
                                    <p className="text-sm text-muted-foreground">Active Sources</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <Zap className="h-5 w-5 text-purple-600" />
                                </div>
                                <div>
                                    <p className="text-2xl font-bold">{stats.embeddings_generated}</p>
                                    <p className="text-sm text-muted-foreground">Processed</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <Clock className="h-5 w-5 text-orange-600" />
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Last Updated</p>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(stats.last_updated).toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Quick Actions */}
            {selectedProject && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Sparkles className="h-5 w-5 text-primary" />
                            Quick Actions
                        </CardTitle>
                        <CardDescription>
                            Common tasks to manage your knowledge base
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {getQuickActions().map((action) => (
                                <Button
                                    key={action.id}
                                    variant="outline"
                                    className="h-auto p-4 flex flex-col items-center gap-3 hover:bg-primary/5 hover:border-primary/30 transition-colors"
                                    onClick={action.action}
                                >
                                    <div className="flex items-center gap-2">
                                        {action.icon}
                                        {action.status === 'completed' && (
                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                        )}
                                    </div>
                                    <div className="text-center">
                                        <p className="font-medium text-sm">{action.title}</p>
                                        <p className="text-xs text-muted-foreground">{action.description}</p>
                                    </div>
                                </Button>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Navigation Tabs */}
            {selectedProject && (
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                                <Button
                                    variant={activeView === 'overview' ? 'default' : 'ghost'}
                                    onClick={() => setActiveView('overview')}
                                    className="flex items-center gap-2"
                                >
                                    <BookOpen className="h-4 w-4" />
                                    Overview
                                </Button>
                                <Button
                                    variant={activeView === 'documents' ? 'default' : 'ghost'}
                                    onClick={() => setActiveView('documents')}
                                    className="flex items-center gap-2"
                                >
                                    <FileText className="h-4 w-4" />
                                    Documents
                                </Button>
                                <Button
                                    variant={activeView === 'sources' ? 'default' : 'ghost'}
                                    onClick={() => setActiveView('sources')}
                                    className="flex items-center gap-2"
                                >
                                    <Globe className="h-4 w-4" />
                                    Sources
                                </Button>
                                <Button
                                    variant={activeView === 'settings' ? 'default' : 'ghost'}
                                    onClick={() => setActiveView('settings')}
                                    className="flex items-center gap-2"
                                >
                                    <Settings className="h-4 w-4" />
                                    Settings
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {activeView === 'overview' && (
                            <div className="space-y-6">
                                <Alert>
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertTitle>Getting Started</AlertTitle>
                                    <AlertDescription>
                                        Your knowledge base is ready! Start by uploading documents or connecting data sources.
                                    </AlertDescription>
                                </Alert>

                                <div className="grid md:grid-cols-2 gap-6">
                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="text-lg">Recent Activity</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <p className="text-muted-foreground">No recent activity</p>
                                        </CardContent>
                                    </Card>

                                    <Card>
                                        <CardHeader>
                                            <CardTitle className="text-lg">Next Steps</CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="space-y-3">
                                                <div className="flex items-center gap-3">
                                                    <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                                                        <span className="text-xs font-medium text-primary">1</span>
                                                    </div>
                                                    <span className="text-sm">Upload your first document</span>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                                                        <span className="text-xs font-medium text-muted-foreground">2</span>
                                                    </div>
                                                    <span className="text-sm text-muted-foreground">Process content for AI</span>
                                                </div>
                                                <div className="flex items-center gap-3">
                                                    <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                                                        <span className="text-xs font-medium text-muted-foreground">3</span>
                                                    </div>
                                                    <span className="text-sm text-muted-foreground">Test your AI assistant</span>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        )}

                        {activeView === 'documents' && selectedProject && (
                            <ModernDocumentManager
                                projectId={selectedProject.id}
                                onDocumentUploaded={() => {
                                    // Refresh stats when document is uploaded
                                    const fetchStats = async () => {
                                        try {
                                            const response = await knowledgeBaseService.getProjectStats?.(selectedProject.id)
                                            if (response?.data?.success) {
                                                setStats(response.data.data)
                                            }
                                        } catch (error) {
                                            console.error('Failed to refresh stats:', error)
                                            // Don't show error to user, just log it
                                        }
                                    }
                                    fetchStats()
                                }}
                            />
                        )}

                        {activeView === 'sources' && selectedProject && (
                            <ModernSourceManager
                                projectId={selectedProject.id}
                                onSourceAdded={() => {
                                    // Refresh stats when source is added
                                    const fetchStats = async () => {
                                        try {
                                            const response = await knowledgeBaseService.getProjectStats?.(selectedProject.id)
                                            if (response?.data?.success) {
                                                setStats(response.data.data)
                                            }
                                        } catch (error) {
                                            console.error('Failed to refresh stats:', error)
                                            // Don't show error to user, just log it
                                        }
                                    }
                                    fetchStats()
                                }}
                            />
                        )}

                        {activeView === 'settings' && selectedProject && (
                            <ModernSettingsManager
                                projectId={selectedProject.id}
                                projectName={selectedProject.name}
                            />
                        )}
                    </CardContent>
                </Card>
            )}

            {/* Project Creation Dialog */}
            <ModernProjectDialog
                open={showCreateProject}
                onClose={() => setShowCreateProject(false)}
                onProjectCreated={handleProjectCreated}
            />
        </div>
    )
}
