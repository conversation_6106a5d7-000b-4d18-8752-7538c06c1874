import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
    FileText,
    Globe,
    Database,
    Settings,
    Plus,
    Upload,
    Link,
    Zap,
    CheckCircle,
    AlertCircle,
    Clock,
    ArrowRight,
    BookOpen,
    Brain,
    Sparkles
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/use-auth'
import { ModernProjectDialog } from '@/components/knowledge-base/modern-project-dialog'
import { ModernDocumentManager } from '@/components/knowledge-base/modern-document-manager'
import { ModernSourceManager } from '@/components/knowledge-base/modern-source-manager'
import { ModernSettingsManager } from '@/components/knowledge-base/modern-settings-manager'
import { ModernProjectListing } from '@/components/knowledge-base/modern-project-listing'

// Types for the new interface
interface Project {
    id: number
    name: string
    description?: string
    created_at: string
    documents_count?: number
    sources_count?: number
    status?: string
}

interface KnowledgeStats {
    total_documents: number
    total_sources: number
    active_sources: number
    embeddings_generated: number
    last_updated: string
}

interface QuickAction {
    id: string
    title: string
    description: string
    icon: React.ReactNode
    action: () => void
    status?: 'available' | 'processing' | 'completed'
}

export default function KnowledgeBaseModule() {
    const { user } = useAuth()
    const [projects, setProjects] = useState<Project[]>([])
    const [selectedProject, setSelectedProject] = useState<Project | null>(null)
    const [stats, setStats] = useState<KnowledgeStats | null>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [activeView, setActiveView] = useState<'overview' | 'projects' | 'documents' | 'sources' | 'settings'>('overview')
    const [showCreateProject, setShowCreateProject] = useState(false)

    // Fetch projects and stats
    useEffect(() => {
        const fetchData = async () => {
            if (!user) return

            setIsLoading(true)
            try {
                const response = await knowledgeBaseService.getProjects()
                if (response?.data?.success) {
                    const projectsData = response.data.data || []
                    setProjects(projectsData)

                    // Auto-select first project if available
                    if (projectsData.length > 0 && !selectedProject) {
                        setSelectedProject(projectsData[0])
                    }
                }
            } catch (error) {
                console.error('Failed to load projects:', error)
                toast.error('Failed to load knowledge base data')
            } finally {
                setIsLoading(false)
            }
        }

        fetchData()
    }, [user])

    // Fetch stats when project changes
    useEffect(() => {
        const fetchStats = async () => {
            if (!selectedProject) return

            try {
                // This would be a new API endpoint for getting project stats
                const response = await knowledgeBaseService.getProjectStats?.(selectedProject.id)
                if (response?.data?.success) {
                    setStats(response.data.data)
                } else {
                    // Provide default stats if API fails
                    setStats({
                        total_documents: 0,
                        total_sources: 0,
                        active_sources: 0,
                        embeddings_generated: 0,
                        last_updated: new Date().toISOString()
                    })
                }
            } catch (error) {
                console.error('Failed to load project stats:', error)
                // Provide default stats on error
                setStats({
                    total_documents: 0,
                    total_sources: 0,
                    active_sources: 0,
                    embeddings_generated: 0,
                    last_updated: new Date().toISOString()
                })
            }
        }

        fetchStats()
    }, [selectedProject])

    // Handle project creation
    const handleProjectCreated = (newProject: Project) => {
        setProjects(prev => [...prev, newProject])
        setSelectedProject(newProject)
        setShowCreateProject(false)
        setActiveView('overview') // Switch to overview of the new project
        toast.success(`Knowledge base "${newProject.name}" created successfully!`)
    }

    // Quick actions for the current view
    const getQuickActions = (): QuickAction[] => {
        if (!selectedProject) return []

        return [
            {
                id: 'upload-documents',
                title: 'Upload Documents',
                description: 'Add PDFs, Word docs, or text files',
                icon: <Upload className="h-5 w-5" />,
                action: () => setActiveView('documents'),
                status: 'available'
            },
            {
                id: 'scrape-website',
                title: 'Import from Website',
                description: 'Extract content from web pages',
                icon: <Link className="h-5 w-5" />,
                action: () => setActiveView('sources'),
                status: 'available'
            },
            {
                id: 'connect-database',
                title: 'Connect Database',
                description: 'Link to external data sources',
                icon: <Database className="h-5 w-5" />,
                action: () => setActiveView('sources'),
                status: 'available'
            },
            {
                id: 'generate-embeddings',
                title: 'Process Content',
                description: 'Make your content AI-searchable',
                icon: <Zap className="h-5 w-5" />,
                action: () => {
                    toast.info('Processing content...')
                },
                status: stats?.embeddings_generated ? 'completed' : 'available'
            }
        ]
    }

    if (!user) {
        return (
            <Card className="max-w-2xl mx-auto">
                <CardHeader className="text-center">
                    <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                        <BookOpen className="h-8 w-8 text-primary" />
                    </div>
                    <CardTitle>Knowledge Base</CardTitle>
                    <CardDescription>
                        Please log in to access your knowledge base
                    </CardDescription>
                </CardHeader>
            </Card>
        )
    }

    if (isLoading) {
        return (
            <Card className="max-w-4xl mx-auto">
                <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-muted-foreground">Loading your knowledge base...</p>
                    </div>
                </CardContent>
            </Card>
        )
    }

    if (projects.length === 0) {
        return (
            <div className="max-w-4xl mx-auto space-y-6">
                {/* Welcome Header */}
                <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                    <CardHeader className="text-center">
                        <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center mb-4">
                            <Brain className="h-8 w-8 text-white" />
                        </div>
                        <CardTitle className="text-2xl">Welcome to AI Knowledge Base</CardTitle>
                        <CardDescription className="text-lg">
                            Teach your AI assistant with your own content and data
                        </CardDescription>
                    </CardHeader>
                </Card>

                {/* Getting Started */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Sparkles className="h-5 w-5 text-primary" />
                            Get Started in 3 Simple Steps
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid md:grid-cols-3 gap-6">
                            <div className="text-center p-6 rounded-lg border border-dashed border-primary/30 hover:border-primary/50 transition-colors">
                                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <Plus className="h-6 w-6 text-primary" />
                                </div>
                                <h3 className="font-semibold mb-2">1. Create Project</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Start by creating your first knowledge base project
                                </p>
                                <Button
                                    onClick={() => setShowCreateProject(true)}
                                    className="w-full"
                                >
                                    Create Project
                                </Button>
                            </div>

                            <div className="text-center p-6 rounded-lg border border-dashed border-muted-foreground/30">
                                <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                                    <Upload className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <h3 className="font-semibold mb-2 text-muted-foreground">2. Add Content</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Upload documents, connect websites, or link databases
                                </p>
                                <Button variant="outline" disabled className="w-full">
                                    Add Content
                                </Button>
                            </div>

                            <div className="text-center p-6 rounded-lg border border-dashed border-muted-foreground/30">
                                <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                                    <Zap className="h-6 w-6 text-muted-foreground" />
                                </div>
                                <h3 className="font-semibold mb-2 text-muted-foreground">3. Activate AI</h3>
                                <p className="text-sm text-muted-foreground mb-4">
                                    Process your content to make it AI-searchable
                                </p>
                                <Button variant="outline" disabled className="w-full">
                                    Process Content
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="flex flex-col space-y-6">
            {/* Page Header - Exact match with other admin pages */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold tracking-tight">Knowledge Base</h1>
                    <p className="text-muted-foreground">
                        Manage AI-powered knowledge bases for enhanced chatbot capabilities
                    </p>
                </div>
                <div className="flex items-center space-x-3">
                    {selectedProject && (
                        <Badge variant="outline" className="px-3 py-1">
                            {selectedProject.name}
                        </Badge>
                    )}
                    <Button
                        variant="outline"
                        onClick={() => setActiveView('projects')}
                    >
                        <Brain className="h-4 w-4 mr-2" />
                        View All Projects
                    </Button>
                    <Button
                        onClick={() => setShowCreateProject(true)}
                        className="bg-blue-600 hover:bg-blue-700"
                    >
                        <Plus className="h-4 w-4 mr-2" />
                        New Project
                    </Button>
                </div>
            </div>

            {/* Stats Overview - Consistent with admin dashboard pattern */}
            {selectedProject && stats && (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
                            <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_documents}</div>
                            <p className="text-xs text-muted-foreground">
                                Uploaded files
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Sources</CardTitle>
                            <Database className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.active_sources}</div>
                            <p className="text-xs text-muted-foreground">
                                Connected data sources
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">AI Processed</CardTitle>
                            <Zap className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.embeddings_generated}</div>
                            <p className="text-xs text-muted-foreground">
                                Ready for AI queries
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {new Date(stats.last_updated).toLocaleDateString()}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Most recent activity
                            </p>
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Quick Actions */}
            {selectedProject && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Sparkles className="h-5 w-5 text-primary" />
                            Quick Actions
                        </CardTitle>
                        <CardDescription>
                            Common tasks to manage your knowledge base
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            {getQuickActions().map((action) => (
                                <Button
                                    key={action.id}
                                    variant="outline"
                                    className="h-auto p-4 flex flex-col items-center gap-3 hover:bg-primary/5 hover:border-primary/30 transition-colors"
                                    onClick={action.action}
                                >
                                    <div className="flex items-center gap-2">
                                        {action.icon}
                                        {action.status === 'completed' && (
                                            <CheckCircle className="h-4 w-4 text-green-500" />
                                        )}
                                    </div>
                                    <div className="text-center">
                                        <p className="font-medium text-sm">{action.title}</p>
                                        <p className="text-xs text-muted-foreground">{action.description}</p>
                                    </div>
                                </Button>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Navigation Tabs */}
            {!selectedProject && activeView !== 'projects' ? (
                <Card>
                    <CardContent className="text-center py-12">
                        <Brain className="h-16 w-16 text-muted-foreground mx-auto mb-6" />
                        <h3 className="text-xl font-semibold mb-2">Welcome to Knowledge Base</h3>
                        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                            Create and manage AI-powered knowledge bases to enhance your chatbot's capabilities with custom data sources.
                        </p>
                        <div className="flex items-center justify-center gap-4">
                            <Button
                                onClick={() => setActiveView('projects')}
                                variant="outline"
                                className="flex items-center gap-2"
                            >
                                <Brain className="h-4 w-4" />
                                View All Projects
                            </Button>
                            <Button
                                onClick={() => setShowCreateProject(true)}
                                className="bg-gradient-to-r from-primary to-primary/80"
                            >
                                <Plus className="h-4 w-4 mr-2" />
                                Create First Project
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            ) : activeView === 'projects' ? (
                <ModernProjectListing
                    onProjectSelect={(project) => {
                        setSelectedProject(project)
                        setActiveView('overview')
                    }}
                    selectedProjectId={selectedProject?.id}
                />
            ) : selectedProject && (
                <div>
                    {/* Navigation Tabs - Consistent with admin panel pattern */}
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8">
                            <button
                                onClick={() => setActiveView('overview')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeView === 'overview'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <BookOpen className="h-4 w-4 inline mr-2" />
                                Overview
                            </button>
                            <button
                                onClick={() => setActiveView('documents')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeView === 'documents'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <FileText className="h-4 w-4 inline mr-2" />
                                Documents
                            </button>
                            <button
                                onClick={() => setActiveView('sources')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeView === 'sources'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <Globe className="h-4 w-4 inline mr-2" />
                                Sources
                            </button>
                            <button
                                onClick={() => setActiveView('settings')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${activeView === 'settings'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                    }`}
                            >
                                <Settings className="h-4 w-4 inline mr-2" />
                                Settings
                            </button>
                        </nav>
                    </div>

                    {/* Tab Content */}
                    <Card className="mt-6">
                        <CardContent className="p-6">
                            {activeView === 'overview' && (
                                <div className="space-y-6">
                                    <Alert>
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertTitle>Getting Started</AlertTitle>
                                        <AlertDescription>
                                            Your knowledge base is ready! Start by uploading documents or connecting data sources.
                                        </AlertDescription>
                                    </Alert>

                                    <div className="grid md:grid-cols-2 gap-6">
                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-lg">Recent Activity</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <p className="text-muted-foreground">No recent activity</p>
                                            </CardContent>
                                        </Card>

                                        <Card>
                                            <CardHeader>
                                                <CardTitle className="text-lg">Next Steps</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="space-y-3">
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                                                            <span className="text-xs font-medium text-primary">1</span>
                                                        </div>
                                                        <span className="text-sm">Upload your first document</span>
                                                    </div>
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                                                            <span className="text-xs font-medium text-muted-foreground">2</span>
                                                        </div>
                                                        <span className="text-sm text-muted-foreground">Process content for AI</span>
                                                    </div>
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-6 h-6 bg-muted rounded-full flex items-center justify-center">
                                                            <span className="text-xs font-medium text-muted-foreground">3</span>
                                                        </div>
                                                        <span className="text-sm text-muted-foreground">Test your AI assistant</span>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </div>
                            )}

                            {activeView === 'documents' && selectedProject && (
                                <ModernDocumentManager
                                    projectId={selectedProject.id}
                                    onDocumentUploaded={() => {
                                        // Refresh stats when document is uploaded
                                        const fetchStats = async () => {
                                            try {
                                                const response = await knowledgeBaseService.getProjectStats?.(selectedProject.id)
                                                if (response?.data?.success) {
                                                    setStats(response.data.data)
                                                }
                                            } catch (error) {
                                                console.error('Failed to refresh stats:', error)
                                                // Don't show error to user, just log it
                                            }
                                        }
                                        fetchStats()
                                    }}
                                />
                            )}

                            {activeView === 'sources' && selectedProject && (
                                <ModernSourceManager
                                    projectId={selectedProject.id}
                                    onSourceAdded={() => {
                                        // Refresh stats when source is added
                                        const fetchStats = async () => {
                                            try {
                                                const response = await knowledgeBaseService.getProjectStats?.(selectedProject.id)
                                                if (response?.data?.success) {
                                                    setStats(response.data.data)
                                                }
                                            } catch (error) {
                                                console.error('Failed to refresh stats:', error)
                                                // Don't show error to user, just log it
                                            }
                                        }
                                        fetchStats()
                                    }}
                                />
                            )}

                            {activeView === 'settings' && selectedProject && (
                                <ModernSettingsManager
                                    projectId={selectedProject.id}
                                    projectName={selectedProject.name}
                                />
                            )}
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* Project Creation Dialog */}
            <ModernProjectDialog
                open={showCreateProject}
                onClose={() => setShowCreateProject(false)}
                onProjectCreated={handleProjectCreated}
            />
        </div>
    )
}
