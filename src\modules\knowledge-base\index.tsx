import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
    FileText,
    Globe,
    Database,
    Settings,
    Plus,
    Upload,
    Link,
    Zap,
    CheckCircle,
    AlertCircle,
    Clock,
    ArrowRight,
    BookOpen,
    Brain,
    Sparkles
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/use-auth'

export default function KnowledgeBaseModule() {
    const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null)
    const [selectedTab, setSelectedTab] = useState('documents')
    const [isProjectSelected, setIsProjectSelected] = useState(false)
    const [projects, setProjects] = useState<any[]>([])
    const [refreshTrigger, setRefreshTrigger] = useState(0)
    const [isLoading, setIsLoading] = useState(false)

    // Fetch projects when component mounts or refreshTrigger changes
    useEffect(() => {
        const fetchProjects = async () => {
            setIsLoading(true)
            try {
                const response = await knowledgeBaseService.getProjects()
                if (response?.data?.success && response.data?.data) {
                    setProjects(response.data.data)
                }
            } catch (error) {
                console.error('Failed to fetch projects:', error)
                toast.error('Failed to load knowledge bases')
            } finally {
                setIsLoading(false)
            }
        }

        fetchProjects()
    }, [refreshTrigger])

    useEffect(() => {
        setIsProjectSelected(selectedProjectId !== null)
    }, [selectedProjectId])

    const handleRefresh = () => {
        setRefreshTrigger(prev => prev + 1)
    }

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Knowledge Base</CardTitle>
                    <CardDescription>
                        Manage document sources, web scraping, database connections, and context rules
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="mb-6">
                        <label className="block text-sm font-medium mb-2">
                            Select Project
                        </label>
                        <ProjectSelector
                            projects={projects}
                            selectedProjectId={selectedProjectId}
                            setSelectedProjectId={setSelectedProjectId}
                            onRefreshProjects={handleRefresh}
                        />
                    </div>

                    {!isProjectSelected && (
                        <Alert variant="default" className="mb-4">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Project Required</AlertTitle>
                            <AlertDescription>
                                Please select a project to manage its knowledge base.
                            </AlertDescription>
                        </Alert>
                    )}

                    <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
                        <TabsList className="w-full">
                            <TabsTrigger value="documents" className="flex-1">Documents</TabsTrigger>
                            <TabsTrigger value="search" className="flex-1">Search</TabsTrigger>
                            <TabsTrigger value="web-scraping" className="flex-1">Web Scraping</TabsTrigger>
                            <TabsTrigger value="database" className="flex-1">Database</TabsTrigger>
                            <TabsTrigger value="context" className="flex-1">Context</TabsTrigger>
                        </TabsList>
                        <div className="mt-6">
                            <TabsContent value="documents">
                                <DocumentsTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="search">
                                <SearchTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="web-scraping">
                                <WebScrapingTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="database">
                                <DatabaseTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                            <TabsContent value="context">
                                <ContextTab
                                    projects={projects}
                                    selectedProjectId={selectedProjectId}
                                    setSelectedProjectId={setSelectedProjectId}
                                    refreshTrigger={refreshTrigger}
                                    onRefresh={handleRefresh}
                                />
                            </TabsContent>
                        </div>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    )
}
