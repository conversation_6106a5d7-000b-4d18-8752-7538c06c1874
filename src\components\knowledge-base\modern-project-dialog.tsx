import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Brain, 
  FileText, 
  Globe, 
  Database, 
  Sparkles,
  ArrowRight,
  Check
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'

interface ModernProjectDialogProps {
  open: boolean
  onClose: () => void
  onProjectCreated: (project: any) => void
}

interface ProjectTemplate {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  features: string[]
  recommended?: boolean
}

export function ModernProjectDialog({ open, onClose, onProjectCreated }: ModernProjectDialogProps) {
  const [step, setStep] = useState<'template' | 'details' | 'creating'>('template')
  const [selectedTemplate, setSelectedTemplate] = useState<ProjectTemplate | null>(null)
  const [projectName, setProjectName] = useState('')
  const [projectDescription, setProjectDescription] = useState('')
  const [isCreating, setIsCreating] = useState(false)

  const templates: ProjectTemplate[] = [
    {
      id: 'general',
      name: 'General Knowledge Base',
      description: 'Perfect for company documentation, FAQs, and general content',
      icon: <Brain className="h-6 w-6" />,
      features: ['Document uploads', 'Web scraping', 'AI processing'],
      recommended: true
    },
    {
      id: 'documentation',
      name: 'Documentation Hub',
      description: 'Ideal for technical documentation and user guides',
      icon: <FileText className="h-6 w-6" />,
      features: ['Document management', 'Version control', 'Search optimization']
    },
    {
      id: 'website',
      name: 'Website Content',
      description: 'Import and sync content from your website automatically',
      icon: <Globe className="h-6 w-6" />,
      features: ['Web scraping', 'Auto-sync', 'Content monitoring']
    },
    {
      id: 'database',
      name: 'Data Integration',
      description: 'Connect to databases and structured data sources',
      icon: <Database className="h-6 w-6" />,
      features: ['Database connections', 'SQL queries', 'Data sync']
    }
  ]

  const handleTemplateSelect = (template: ProjectTemplate) => {
    setSelectedTemplate(template)
    setProjectName(template.name)
    setProjectDescription(template.description)
    setStep('details')
  }

  const handleCreateProject = async () => {
    if (!projectName.trim()) {
      toast.error('Please enter a project name')
      return
    }

    setIsCreating(true)
    setStep('creating')

    try {
      const response = await knowledgeBaseService.createProject({
        name: projectName,
        description: projectDescription,
        template: selectedTemplate?.id || 'general'
      })

      if (response?.data?.success) {
        toast.success('Project created successfully!')
        onProjectCreated(response.data.data)
        onClose()
        resetForm()
      } else {
        throw new Error(response?.data?.message || 'Failed to create project')
      }
    } catch (error) {
      console.error('Failed to create project:', error)
      toast.error('Failed to create project. Please try again.')
      setStep('details')
    } finally {
      setIsCreating(false)
    }
  }

  const resetForm = () => {
    setStep('template')
    setSelectedTemplate(null)
    setProjectName('')
    setProjectDescription('')
    setIsCreating(false)
  }

  const handleClose = () => {
    if (!isCreating) {
      resetForm()
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
            Create New Knowledge Base
          </DialogTitle>
        </DialogHeader>

        {step === 'template' && (
          <div className="space-y-6">
            <div className="text-center">
              <p className="text-muted-foreground">
                Choose a template to get started quickly with pre-configured settings
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              {templates.map((template) => (
                <Card 
                  key={template.id}
                  className={`cursor-pointer transition-all hover:shadow-md hover:border-primary/30 ${
                    template.recommended ? 'ring-2 ring-primary/20 bg-primary/5' : ''
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        {template.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{template.name}</h3>
                          {template.recommended && (
                            <Badge variant="secondary" className="text-xs">
                              Recommended
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {template.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {template.features.map((feature) => (
                            <Badge key={feature} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <ArrowRight className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {step === 'details' && selectedTemplate && (
          <div className="space-y-6">
            <div className="flex items-center gap-3 p-4 bg-primary/5 rounded-lg">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                {selectedTemplate.icon}
              </div>
              <div>
                <h3 className="font-semibold">{selectedTemplate.name}</h3>
                <p className="text-sm text-muted-foreground">{selectedTemplate.description}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="project-name">Project Name *</Label>
                <Input
                  id="project-name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="Enter a name for your knowledge base"
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="project-description">Description (Optional)</Label>
                <Textarea
                  id="project-description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  placeholder="Describe what this knowledge base will contain"
                  rows={3}
                  className="text-base"
                />
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setStep('template')}>
                Back to Templates
              </Button>
              <Button onClick={handleCreateProject} disabled={!projectName.trim()}>
                Create Project
              </Button>
            </div>
          </div>
        )}

        {step === 'creating' && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
            <h3 className="text-lg font-semibold mb-2">Creating Your Knowledge Base</h3>
            <p className="text-muted-foreground">
              Setting up your project with the selected template...
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
