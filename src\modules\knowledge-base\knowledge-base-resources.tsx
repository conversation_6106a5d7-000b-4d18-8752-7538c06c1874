// This file is now deprecated - using the new modern interface in index.tsx
// Redirecting to the new interface

import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export function KnowledgeBaseResources() {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to the new modern interface
    navigate('/knowledge-base', { replace: true });
  }, [navigate]);

  return null;
}

// Define FileMeta type for use in other components
export interface FileMeta {
  id: number;
  file_name: string;
  file_type: string;
  file_size: number;
  category?: string;
  created_at: string;
  has_embeddings: boolean;
  embeddings_count?: number;
  embeddings_provider?: string;
  embeddings_model?: string;
  embeddings_generated_at?: string;
  is_active_source: boolean;
}

// This component is deprecated - redirecting to new modern interface
