<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\KnowledgeSource;
use App\Models\KnowledgeDocument;
use App\Models\KnowledgeEmbedding;
use App\Models\Project;
use App\Models\ScrapedUrl;
use App\Models\AIModel;
use App\Models\ContextRule;
use App\Models\ContextSetting;
use App\Models\DatabaseConnection;
use App\Services\VectorEmbeddingService;
use App\Services\ScraperService;
use App\Services\DocumentProcessingService;
use App\Services\ContextRuleService;
use App\Services\DatabaseConnectionService;
use App\Services\AuditLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Http\JsonResponse;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class KnowledgeBaseController extends Controller
{
    /**
     * @var VectorEmbeddingService
     */
    protected $embeddingService;

    /**
     * @var ScraperService
     */
    protected $scraperService;

    /**
     * @var DocumentProcessingService
     */
    protected $documentService;

    /**
     * @var ContextRuleService
     */
    protected $contextRuleService;

    /**
     * @var DatabaseConnectionService
     */
    protected $databaseService;

    /**
     * @var AuditLogService
     */
    protected $auditLogService;

    /**
     * Constructor.
     */
    public function __construct(
        VectorEmbeddingService $embeddingService,
        ScraperService $scraperService,
        DocumentProcessingService $documentService,
        ContextRuleService $contextRuleService,
        DatabaseConnectionService $databaseService,
        AuditLogService $auditLogService
    ) {
        $this->embeddingService = $embeddingService;
        $this->scraperService = $scraperService;
        $this->documentService = $documentService;
        $this->contextRuleService = $contextRuleService;
        $this->databaseService = $databaseService;
        $this->auditLogService = $auditLogService;
    }

    /**
     * Get all knowledge sources.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $filters = $request->all();

        $query = KnowledgeSource::query();

        if (isset($filters['project_id'])) {
            $query->where('project_id', $filters['project_id']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        $sources = $query->latest()->paginate(15);

        return response()->json($sources);
    }

    /**
     * Store a new knowledge source.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:file,database,scrape',
            'description' => 'nullable|string',
            'project_id' => 'required|exists:projects,id',
            'status' => 'nullable|string|in:active,inactive',
            'priority' => 'nullable|integer|min:0|max:100',
            'config' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $source = KnowledgeSource::create([
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'project_id' => $request->project_id,
            'status' => $request->status ?? 'active',
            'priority' => $request->priority ?? 50,
            'config' => $request->config,
            'embedding_model_id' => $request->model_id,
        ]);

        return response()->json(['success' => true, 'data' => $source]);
    }

    /**
     * Get a specific knowledge source.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $source = KnowledgeSource::with(['documents'])->findOrFail($id);
        return response()->json($source);
    }

    /**
     * Update a knowledge source.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $source = KnowledgeSource::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|string|in:active,inactive',
            'priority' => 'nullable|integer|min:0|max:100',
            'config' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $source->update($request->all());

        return response()->json(['success' => true, 'data' => $source]);
    }

    /**
     * Delete a knowledge source.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $source = KnowledgeSource::findOrFail($id);

        // Delete all associated documents
        foreach ($source->documents as $document) {
            $this->deleteDocument($document->id);
        }

        $source->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Upload a document to the knowledge base.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadDocument(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:102400', // 100MB
            'category' => 'nullable|string',
            'source_id' => 'nullable|exists:knowledge_sources,id',
            'project_id' => 'required|exists:projects,id',
            'is_active_source' => 'nullable|boolean',
            'model_id' => 'nullable|exists:ai_models,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $file = $request->file('file');
        $fileName = $file->getClientOriginalName();
        $fileSize = $file->getSize();
        $fileMimeType = $file->getMimeType();
        $fileExtension = strtolower($file->getClientOriginalExtension());

        // Validate file format
        $formatValidation = $this->documentService->validateDocument(
            $file->getPathname(),
            $fileName,
            $fileMimeType
        );

        if (!$formatValidation['valid']) {
            return response()->json([
                'success' => false,
                'errors' => $formatValidation['errors']
            ], 422);
        }

        try {
            // Check if source exists or create one
            $sourceId = $request->input('source_id');

            if (!$sourceId) {
                // Create a default source if one is not provided
                $source = KnowledgeSource::create([
                    'name' => 'Uploaded Documents',
                    'type' => 'file',
                    'description' => 'Automatically created for file uploads',
                    'project_id' => $request->input('project_id'),
                    'status' => 'active',
                    'priority' => 50,
                ]);

                $sourceId = $source->id;
            }

            // Store file in project-specific subdirectory
            $projectId = $request->input('project_id');
            $storageDir = "knowledge-base/projects/{$projectId}/documents";
            $storagePath = $file->store($storageDir, 'private');

            if (!$storagePath) {
                throw new Exception("Failed to store file");
            }

            // Create document record
            $document = Document::create([
                'file_name' => $fileName,
                'file_path' => $storagePath,
                'file_type' => $fileExtension,
                'file_size' => $fileSize,
                'category' => $request->input('category', 'general'),
                'source_id' => $sourceId,
                'project_id' => $projectId,
                'is_active_source' => $request->boolean('is_active_source', true),
                'status' => 'pending',
                'has_embeddings' => false,
                'metadata' => [
                    'mime_type' => $fileMimeType,
                    'upload_time' => now()->toIso8601String(),
                    'original_name' => $fileName,
                ],
            ]);

            // Log the upload
            $this->auditLogService->logActivity(
                'document_upload',
                "Uploaded document {$fileName}",
                [
                    'document_id' => $document->id,
                    'file_name' => $fileName,
                    'file_size' => $fileSize,
                    'category' => $request->input('category', 'general'),
                    'project_id' => $projectId,
                    'source_id' => $sourceId,
                ]
            );

            // If an AI model is specified, generate embeddings asynchronously
            $modelId = $request->input('model_id');
            if ($modelId) {
                // Queue the embedding generation job
                dispatch(new \App\Jobs\GenerateEmbeddingsJob(
                    $document->id,
                    [
                        'model_id' => $modelId,
                        'provider' => 'openai',
                        'chunk_size' => 1000,
                        'chunk_overlap' => 200,
                    ]
                ));

                $document->update([
                    'status' => 'processing',
                    'metadata' => array_merge($document->metadata, [
                        'embedding_job_queued' => now()->toIso8601String(),
                    ]),
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $document->id,
                    'file_name' => $document->file_name,
                    'file_type' => $document->file_type,
                    'file_size' => $document->file_size,
                    'category' => $document->category,
                    'status' => $document->status,
                    'has_embeddings' => $document->has_embeddings,
                    'is_active_source' => $document->is_active_source,
                    'created_at' => $document->created_at->toIso8601String(),
                    'embedding_status' => $modelId ? 'queued' : 'not_started',
                ],
                'message' => 'Document uploaded successfully',
            ]);

        } catch (Exception $e) {
            // Log the error
            Log::error("Error uploading document: " . $e->getMessage(), [
                'file_name' => $fileName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload document',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * List documents for a source.
     *
     * @param Request $request
     * @param int $sourceId
     * @return \Illuminate\Http\JsonResponse
     */
    public function listDocuments(Request $request, $sourceId)
    {
        $source = KnowledgeSource::findOrFail($sourceId);
        $documents = $source->documents()->latest()->paginate(15);

        return response()->json($documents);
    }

    /**
     * List all documents with filtering, pagination, and sorting.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listAllDocuments(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'nullable|exists:projects,id',
            'source_id' => 'nullable|exists:knowledge_sources,id',
            'category' => 'nullable|string',
            'status' => 'nullable|string|in:pending,processing,active,failed',
            'has_embeddings' => 'nullable|boolean',
            'is_active_source' => 'nullable|boolean',
            'search' => 'nullable|string|max:255',
            'sort_by' => 'nullable|string|in:file_name,file_size,category,created_at,updated_at,status',
            'sort_dir' => 'nullable|string|in:asc,desc',
            'per_page' => 'nullable|integer|min:5|max:100',
            'page' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            // Start building the query
            $query = Document::query()->with('source');

            // Apply filters
            if ($request->has('project_id')) {
                $query->where('project_id', $request->input('project_id'));
            }

            if ($request->has('source_id')) {
                $query->where('source_id', $request->input('source_id'));
            }

            if ($request->has('category')) {
                $query->where('category', $request->input('category'));
            }

            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->has('has_embeddings')) {
                $query->where('has_embeddings', $request->boolean('has_embeddings'));
            }

            if ($request->has('is_active_source')) {
                $query->where('is_active_source', $request->boolean('is_active_source'));
            }

            // Search functionality
            if ($request->has('search') && !empty($request->input('search'))) {
                $searchTerm = $request->input('search');
                $query->where(function($q) use ($searchTerm) {
                    $q->where('file_name', 'like', "%{$searchTerm}%")
                      ->orWhere('category', 'like', "%{$searchTerm}%")
                      ->orWhere('file_type', 'like', "%{$searchTerm}%");
                });
            }

            // Apply sorting
            $sortBy = $request->input('sort_by', 'created_at');
            $sortDir = $request->input('sort_dir', 'desc');
            $query->orderBy($sortBy, $sortDir);

            // Apply pagination
            $perPage = $request->input('per_page', 20);
            $documents = $query->paginate($perPage);

            // Prepare the response
            $result = [
                'success' => true,
                'data' => $documents->items(),
                'pagination' => [
                    'total' => $documents->total(),
                    'per_page' => $documents->perPage(),
                    'current_page' => $documents->currentPage(),
                    'last_page' => $documents->lastPage(),
                    'from' => $documents->firstItem(),
                    'to' => $documents->lastItem(),
                ],
                'meta' => [
                    'active_sources_count' => Document::where('is_active_source', true)
                        ->when($request->has('project_id'), function($q) use ($request) {
                            $q->where('project_id', $request->input('project_id'));
                        })
                        ->count(),
                    'documents_with_embeddings_count' => Document::where('has_embeddings', true)
                        ->when($request->has('project_id'), function($q) use ($request) {
                            $q->where('project_id', $request->input('project_id'));
                        })
                        ->count(),
                    'total_document_count' => Document::when($request->has('project_id'), function($q) use ($request) {
                            $q->where('project_id', $request->input('project_id'));
                        })
                        ->count(),
                    'categories' => Document::select('category')
                        ->when($request->has('project_id'), function($q) use ($request) {
                            $q->where('project_id', $request->input('project_id'));
                        })
                        ->distinct()
                        ->pluck('category')
                        ->filter()
                        ->values(),
                ],
            ];

            return response()->json($result);

        } catch (Exception $e) {
            Log::error("Error listing documents: " . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to list documents',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a document.
     *
     * @param Request $request
     * @param int $docId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteDocument(Request $request, $docId)
    {
        $document = Document::findOrFail($docId);

        // Check authorization
        if ($request->user() && !$request->user()->can('delete', $document)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Delete file from storage
        if (Storage::exists($document->file_path)) {
            Storage::delete($document->file_path);
        }

        // Delete embeddings
        $this->embeddingService->deleteEmbeddings($docId);

        // Log the action before deleting
        $this->auditLogService->logDocumentAction(
            'delete',
            $document->id,
            $request->user()->id ?? null,
            [
                'file_name' => $document->file_name,
                'project_id' => $document->project_id,
            ]
        );

        $document->delete();

        return response()->json([
            'success' => true,
            'message' => 'Document deleted successfully'
        ]);
    }

    /**
     * Toggle document as AI source.
     *
     * @param Request $request
     * @param int $docId
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleDocumentAsAISource(Request $request, $docId)
    {
        $document = Document::findOrFail($docId);
        $document->is_active_source = $request->is_active ?? !$document->is_active_source;
        $document->save();

        return response()->json([
            'success' => true,
            'message' => 'Document status updated',
            'document' => $document
        ]);
    }

    /**
     * Generate embeddings for a document.
     *
     * @param Request $request
     * @param int $documentId
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateEmbeddings(Request $request, $documentId)
    {
        $document = Document::findOrFail($documentId);
        $modelId = $request->model_id;

        // Queue embedding generation
        $this->embeddingService->generateEmbeddings($document->id, $modelId);

        return response()->json([
            'success' => true,
            'message' => 'Embedding generation queued'
        ]);
    }

    /**
     * Generate embeddings for multiple documents.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchGenerateEmbeddings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'document_ids' => 'required|array',
            'document_ids.*' => 'exists:documents,id',
            'model_id' => 'nullable|exists:ai_models,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        // Process each document
        foreach ($request->document_ids as $documentId) {
            $this->embeddingService->generateEmbeddings($documentId, $request->model_id);
        }

        return response()->json([
            'success' => true,
            'message' => 'Batch embedding generation queued'
        ]);
    }

    /**
     * Get embedding status.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEmbeddingStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'document_ids' => 'required|array',
            'document_ids.*' => 'exists:documents,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $documents = Document::whereIn('id', $request->document_ids)->get();
        $statuses = [];

        foreach ($documents as $document) {
            $statuses[$document->id] = [
                'has_embeddings' => $document->has_embeddings,
                'embeddings_count' => $document->embeddings_count,
                'embeddings_progress' => $document->embeddings_progress,
                'embeddings_provider' => $document->embeddings_provider,
                'embeddings_model' => $document->embeddings_model,
                'embeddings_updated_at' => $document->embeddings_updated_at,
                'status' => $document->status,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $statuses
        ]);
    }

    /**
     * Get available embedding models.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getEmbeddingModels()
    {
        $models = AIModel::where('is_embedding_model', true)
            ->where('status', 'active')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $models
        ]);
    }

    /**
     * Scrape a URL and extract content.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function scrapeUrl(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'url' => 'required|url',
            'project_id' => 'required|exists:projects,id',
            'table_name' => 'nullable|string',
            'model_id' => 'nullable|exists:ai_models,id',
            'render_js' => 'nullable|boolean',
            'use_cache' => 'nullable|boolean',
            'wait_for_selector' => 'nullable|string',
            'scroll_page' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            // Prepare options for the scraper
            $options = [
                'render_js' => $request->render_js ?? true,
                'use_cache' => $request->use_cache ?? true,
                'scroll_page' => $request->scroll_page ?? false,
                'project_id' => $request->project_id,
            ];

            // Add optional parameters if provided
            if ($request->has('wait_for_selector')) {
                $options['wait_for_selector'] = $request->wait_for_selector;
            }

            // Log the scraping attempt
            Log::info('Scraping URL', [
                'url' => $request->url,
                'project_id' => $request->project_id,
                'user_id' => $request->user()->id ?? null,
                'options' => $options,
            ]);

            // Call the scraper service
            $result = $this->scraperService->scrapeUrl(
                $request->url,
                $request->model_id,
                $options
            );

            // Log success
            Log::info('URL scraped successfully', [
                'url' => $request->url,
                'title' => $result['title'] ?? 'No title',
                'content_length' => strlen($result['text'] ?? ''),
                'is_js_heavy' => $result['is_js_heavy'] ?? false,
            ]);

            // Return the result
            return response()->json([
                'success' => true,
                'url' => $request->url,
                'text' => $result['text'] ?? null,
                'raw' => $result['raw'] ?? null,
                'table' => $result['table'] ?? null,
                'json' => $result['json'] ?? null,
                'title' => $result['title'] ?? null,
                'metadata' => $result['metadata'] ?? null,
                'links' => $result['links'] ?? null,
                'images' => $result['images'] ?? null,
                'is_js_heavy' => $result['is_js_heavy'] ?? false,
                'scraped_at' => $result['scraped_at'] ?? now()->toIso8601String(),
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Failed to scrape URL', [
                'url' => $request->url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to scrape URL: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save scraped content.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveScrape(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'url' => 'required|url',
            'format' => 'required|string|in:text,raw,table,json',
            'project_id' => 'required|exists:projects,id',
            'table_name' => 'required|string',
            'title' => 'nullable|string',
            'model_id' => 'nullable|exists:ai_models,id',
            'metadata' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            // Create or get source
            $source = KnowledgeSource::firstOrCreate(
                [
                    'type' => 'scrape',
                    'project_id' => $request->project_id,
                    'table_name' => $request->table_name,
                ],
                [
                    'name' => 'Web Scraping',
                    'description' => 'Content scraped from the web',
                    'status' => 'active',
                    'created_by' => $request->user()->id ?? null,
                ]
            );

            // Prepare metadata
            $metadata = $request->metadata ?? [];
            $metadata['saved_at'] = now()->toIso8601String();
            $metadata['saved_by'] = $request->user()->id ?? null;
            $metadata['model_id'] = $request->model_id;

            // Save to database using the improved ScraperService
            $result = $this->scraperService->saveToDatabase(
                $request->content,
                $request->format,
                $request->table_name,
                $request->url,
                $request->title,
                $metadata,
                $source->id,
                $request->project_id,
                $request->user()->id ?? null
            );

            // Log the action
            $this->auditLogService->logAction(
                'save_scrape',
                'scraped_url',
                $result['scraped_url_id'] ?? null,
                $request->user()->id ?? null,
                [
                    'url' => $request->url,
                    'format' => $request->format,
                    'table_name' => $request->table_name,
                    'project_id' => $request->project_id,
                    'source_id' => $source->id,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => $result['message'] ?? 'Scraped content saved successfully',
                'data' => [
                    'id' => $result['scraped_url_id'] ?? null,
                    'url' => $request->url,
                    'title' => $request->title,
                    'format' => $request->format,
                    'project_id' => $request->project_id,
                    'source_id' => $source->id,
                    'table_name' => $request->table_name,
                    'is_new' => $result['is_new'] ?? true,
                ]
            ]);
        } catch (\Exception $e) {
            // Log error
            Log::error('Failed to save scraped content', [
                'url' => $request->url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to save scraped content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a list of all scraped content tables.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getScrapedContentTables()
    {
        try {
            $tables = $this->scraperService->getScrapedContentTables();

            return response()->json([
                'success' => true,
                'data' => $tables
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get scraped content tables: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get scheduled scrapes.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getScheduledScrapes(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'nullable|exists:projects,id',
            'status' => 'nullable|string|in:active,paused,completed',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $query = ScheduledScrape::query();

            // Apply filters
            if ($request->has('project_id')) {
                $query->where('project_id', $request->project_id);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            // Get results with pagination
            $scheduledScrapes = $query->orderBy('created_at', 'desc')
                ->paginate($request->per_page ?? 15);

            return response()->json([
                'success' => true,
                'data' => $scheduledScrapes
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get scheduled scrapes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update a scheduled scrape.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveScheduledScrape(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'nullable|exists:scheduled_scrapes,id',
            'name' => 'required|string|max:255',
            'url' => 'required|url',
            'project_id' => 'required|exists:projects,id',
            'table_name' => 'required|string|max:255',
            'format' => 'required|string|in:text,raw,table,json',
            'frequency' => 'required|string|in:hourly,daily,weekly,monthly',
            'model_id' => 'nullable|exists:ai_models,id',
            'status' => 'nullable|string|in:active,paused',
            'options' => 'nullable|array',
            'auth' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            // Prepare data
            $data = $request->only([
                'name', 'url', 'project_id', 'table_name', 'format',
                'frequency', 'model_id', 'status'
            ]);

            // Add options
            $options = $request->options ?? [];

            // Add authentication if provided
            if ($request->has('auth')) {
                $options['auth'] = $request->auth;
            }

            $data['options'] = $options;

            // Set default status if not provided
            if (!isset($data['status'])) {
                $data['status'] = 'active';
            }

            // Set created_by
            $data['created_by'] = $request->user()->id ?? null;

            // Calculate next run time
            $now = now();
            switch ($data['frequency']) {
                case 'hourly':
                    $data['next_run'] = $now->addHour();
                    break;
                case 'daily':
                    $data['next_run'] = $now->addDay();
                    break;
                case 'weekly':
                    $data['next_run'] = $now->addWeek();
                    break;
                case 'monthly':
                    $data['next_run'] = $now->addMonth();
                    break;
                default:
                    $data['next_run'] = $now->addDay();
                    break;
            }

            // Create or update
            if ($request->id) {
                $scheduledScrape = ScheduledScrape::findOrFail($request->id);
                $scheduledScrape->update($data);
                $message = 'Scheduled scrape updated successfully';
            } else {
                $scheduledScrape = ScheduledScrape::create($data);
                $message = 'Scheduled scrape created successfully';
            }

            // Log the action
            $this->auditLogService->logAction(
                $request->id ? 'update' : 'create',
                'scheduled_scrape',
                $scheduledScrape->id,
                $request->user()->id ?? null,
                [
                    'name' => $scheduledScrape->name,
                    'url' => $scheduledScrape->url,
                    'project_id' => $scheduledScrape->project_id,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => $scheduledScrape
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save scheduled scrape: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a scheduled scrape.
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteScheduledScrape($id, Request $request)
    {
        try {
            $scheduledScrape = ScheduledScrape::findOrFail($id);

            // Log the action before deletion
            $this->auditLogService->logAction(
                'delete',
                'scheduled_scrape',
                $scheduledScrape->id,
                $request->user()->id ?? null,
                [
                    'name' => $scheduledScrape->name,
                    'url' => $scheduledScrape->url,
                    'project_id' => $scheduledScrape->project_id,
                ]
            );

            // Delete the scheduled scrape
            $scheduledScrape->delete();

            return response()->json([
                'success' => true,
                'message' => 'Scheduled scrape deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete scheduled scrape: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Run a scheduled scrape immediately.
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function runScheduledScrape($id, Request $request)
    {
        try {
            $scheduledScrape = ScheduledScrape::findOrFail($id);

            // Dispatch a job to run the scrape
            dispatch(new RunScheduledScrapeJob($scheduledScrape));

            return response()->json([
                'success' => true,
                'message' => 'Scheduled scrape has been queued to run'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to run scheduled scrape: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * List scraped URLs.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function listScrapedUrls(Request $request)
    {
        $query = ScrapedUrl::query();

        if ($request->has('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        if ($request->has('source_id')) {
            $query->where('source_id', $request->source_id);
        }

        if ($request->has('table_name')) {
            $query->where('table_name', $request->table_name);
        }

        $scrapedUrls = $query->latest()->paginate(15);

        return response()->json($scrapedUrls);
    }

    /**
     * Delete a scraped URL.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteScrapedUrl($id)
    {
        $scrapedUrl = ScrapedUrl::findOrFail($id);
        $scrapedUrl->delete();

        return response()->json([
            'success' => true,
            'message' => 'Scraped URL deleted successfully'
        ]);
    }

    /**
     * Get context settings for a project.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContextSettings(Request $request, $projectId)
    {
        try {
            $settings = $this->contextRuleService->getContextSettings($projectId);

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get context settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update context settings for a project.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateContextSettings(Request $request, $projectId)
    {
        $validator = Validator::make($request->all(), [
            'priority' => 'nullable|array',
            'contextRetention' => 'nullable|string|in:session,conversation,permanent',
            'relevanceThreshold' => 'nullable|numeric|min:0|max:1',
            'maxSourcesPerQuery' => 'nullable|integer|min:1|max:10',
            'enabledSources' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $settings = $this->contextRuleService->updateContextSettings($projectId, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Context settings updated successfully',
                'data' => $settings
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update context settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get context rules for a project.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getContextRules(Request $request, $projectId)
    {
        try {
            $rules = $this->contextRuleService->getContextRules($projectId);

            return response()->json([
                'success' => true,
                'data' => $rules
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get context rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save a new context rule.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveContextRule(Request $request, $projectId)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sources' => 'nullable|array',
            'keywords' => 'nullable|array',
            'priority' => 'nullable|integer|min:1|max:100',
            'active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $rule = $this->contextRuleService->saveContextRule($projectId, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Context rule created successfully',
                'data' => $rule
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create context rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing context rule.
     *
     * @param Request $request
     * @param int $projectId
     * @param int $ruleId
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateContextRule(Request $request, $projectId, $ruleId)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'sources' => 'nullable|array',
            'keywords' => 'nullable|array',
            'priority' => 'nullable|integer|min:1|max:100',
            'active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $rule = $this->contextRuleService->updateContextRule($projectId, $ruleId, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Context rule updated successfully',
                'data' => $rule
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update context rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a context rule.
     *
     * @param Request $request
     * @param int $projectId
     * @param int $ruleId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteContextRule(Request $request, $projectId, $ruleId)
    {
        try {
            $result = $this->contextRuleService->deleteContextRule($projectId, $ruleId);

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Context rule deleted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Context rule not found'
                ], 404);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete context rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test a query against context rules.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function testContextRule(Request $request, $projectId)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $results = $this->contextRuleService->testContextRule($projectId, $request->query);

            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test context rule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search for similar documents based on a query using vector similarity.
     *
     * This endpoint uses AI embeddings to perform semantic search across documents.
     * It returns documents ranked by similarity to the query.
     *
     * @param Request $request The request with search parameters:
     *                         - query: The search query text (required)
     *                         - project_id: Project ID to search within (required)
     *                         - limit: Maximum number of results to return (optional, default: 10)
     *                         - threshold: Minimum similarity threshold, 0-1 (optional, default: 0.7)
     *                         - filter: Object with optional filters like category, file_type (optional)
     *                         - model_id: Specific embedding model ID to use (optional)
     * @return JsonResponse JSON response with search results or error message
     */
    public function searchSimilarDocuments(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3',
            'project_id' => 'required|exists:projects,id',
            'limit' => 'nullable|integer|min:1|max:50',
            'threshold' => 'nullable|numeric|min:0|max:1',
            'filter' => 'nullable|array',
            'model_id' => 'nullable|exists:ai_models,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $query = $request->input('query');
            $projectId = $request->input('project_id');
            $limit = $request->input('limit', 10);
            $threshold = $request->input('threshold', 0.7);
            $filter = $request->input('filter', []);
            $modelId = $request->input('model_id');

            // Get embeddings for the query
            $queryEmbedding = $this->embeddingService->generateQueryEmbedding(
                $query,
                'openai',
                ['model_id' => $modelId]
            );

            if (!$queryEmbedding) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate embedding for query',
                ], 500);
            }

            // Build a query to find similar documents
            $documentQuery = Document::where('project_id', $projectId)
                ->where('has_embeddings', true)
                ->where('is_active_source', true);

            // Apply additional filters if provided
            if (isset($filter['category']) && !empty($filter['category'])) {
                $documentQuery->where('category', $filter['category']);
            }

            if (isset($filter['source_id']) && !empty($filter['source_id'])) {
                $documentQuery->where('source_id', $filter['source_id']);
            }

            if (isset($filter['file_type']) && !empty($filter['file_type'])) {
                $documentQuery->where('file_type', $filter['file_type']);
            }

            // Get document IDs for the search
            $documentIds = $documentQuery->pluck('id')->toArray();

            if (empty($documentIds)) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'message' => 'No documents found matching the criteria',
                ]);
            }

            // Get similar documents based on vector similarity
            $results = $this->embeddingService->searchSimilarDocuments(
                $queryEmbedding,
                $documentIds,
                $limit,
                $threshold
            );

            if (!$results || empty($results)) {
                return response()->json([
                    'success' => true,
                    'data' => [],
                    'message' => 'No similar documents found',
                ]);
            }

            // Get document details for the results
            $documentIds = array_column($results, 'document_id');
            $documents = Document::whereIn('id', $documentIds)->get();

            // Map documents to results with similarity scores
            $formattedResults = $documents->map(function ($document) use ($results) {
                $resultItem = collect($results)->firstWhere('document_id', $document->id);
                return [
                    'id' => $document->id,
                    'file_name' => $document->file_name,
                    'file_type' => $document->file_type,
                    'category' => $document->category,
                    'similarity_score' => $resultItem['similarity'] ?? 0,
                    'chunk_text' => $resultItem['chunk_text'] ?? null,
                    'source_id' => $document->source_id,
                    'created_at' => $document->created_at->toIso8601String(),
                ];
            });

            // Sort by similarity score (highest first)
            $formattedResults = $formattedResults->sortByDesc('similarity_score')->values();

            return response()->json([
                'success' => true,
                'data' => $formattedResults,
                'meta' => [
                    'query' => $query,
                    'threshold' => $threshold,
                    'total_results' => count($formattedResults),
                ],
            ]);

        } catch (Exception $e) {
            Log::error("Error searching similar documents: " . $e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to search documents',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get project statistics.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProjectStats(Request $request, $projectId)
    {
        try {
            // Get document counts
            $totalDocuments = KnowledgeDocument::where('project_id', $projectId)->count();
            $documentsWithEmbeddings = KnowledgeDocument::where('project_id', $projectId)
                ->where('has_embeddings', true)
                ->count();

            // Get source counts
            $totalSources = KnowledgeSource::where('project_id', $projectId)->count();
            $activeSources = KnowledgeSource::where('project_id', $projectId)
                ->where('status', 'active')
                ->count();

            // Get embedding counts - try different tables that might exist
            $embeddingsGenerated = 0;
            try {
                // Try knowledge_embeddings table first
                if (Schema::hasTable('knowledge_embeddings')) {
                    $embeddingsGenerated = DB::table('knowledge_embeddings')
                        ->join('knowledge_documents', 'knowledge_embeddings.document_id', '=', 'knowledge_documents.id')
                        ->where('knowledge_documents.project_id', $projectId)
                        ->count();
                } elseif (Schema::hasTable('knowledge_document_embeddings')) {
                    // Fallback to knowledge_document_embeddings table
                    $embeddingsGenerated = DB::table('knowledge_document_embeddings')
                        ->join('knowledge_documents', 'knowledge_document_embeddings.document_id', '=', 'knowledge_documents.id')
                        ->where('knowledge_documents.project_id', $projectId)
                        ->count();
                } else {
                    // Fallback to embeddings_count field in documents
                    $embeddingsGenerated = KnowledgeDocument::where('project_id', $projectId)
                        ->sum('embeddings_count') ?? 0;
                }
            } catch (Exception $e) {
                // If embedding count fails, just use 0
                $embeddingsGenerated = 0;
            }

            // Get last updated timestamp
            $lastUpdated = KnowledgeDocument::where('project_id', $projectId)
                ->latest('updated_at')
                ->first()?->updated_at ?? now();

            $stats = [
                'total_documents' => $totalDocuments,
                'total_sources' => $totalSources,
                'active_sources' => $activeSources,
                'embeddings_generated' => $embeddingsGenerated,
                'last_updated' => $lastUpdated->toISOString(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (Exception $e) {
            \Log::error('Failed to get project stats: ' . $e->getMessage(), [
                'project_id' => $projectId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get project stats: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all tables for a project.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProjectTables(Request $request, $projectId)
    {
        try {
            $tables = $this->databaseService->getTables($projectId);

            return response()->json([
                'success' => true,
                'data' => $tables
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get project tables: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get table details.
     *
     * @param Request $request
     * @param int $projectId
     * @param string $tableName
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTableDetails(Request $request, $projectId, $tableName)
    {
        try {
            $tableDetails = $this->databaseService->getTableDetails($projectId, $tableName);

            if (!$tableDetails) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $tableDetails
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get table details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute a read-only query.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function executeQuery(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'query' => 'required|string',
            'limit' => 'nullable|integer|min:1|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            $results = $this->databaseService->executeReadOnlyQuery(
                $request->project_id,
                $request->query,
                $request->limit ?? 100
            );

            if (isset($results['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $results['error']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $results
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to execute query: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add a table to the knowledge base.
     *
     * @param Request $request
     * @param int $projectId
     * @param string $tableName
     * @return \Illuminate\Http\JsonResponse
     */
    public function addTableToKnowledgeBase(Request $request, $projectId, $tableName)
    {
        $validator = Validator::make($request->all(), [
            'embedModel' => 'required|exists:ai_models,id',
            'autoSync' => 'nullable|boolean',
            'syncFrequency' => 'nullable|in:hourly,daily,weekly,monthly',
            'embedAll' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            // Check if table exists
            $tableDetails = $this->databaseService->getTableDetails($projectId, $tableName);

            if (!$tableDetails) {
                return response()->json([
                    'success' => false,
                    'message' => 'Table not found'
                ], 404);
            }

            // Get the database connection for this project
            $connection = DatabaseConnection::where('project_id', $projectId)->first();

            if (!$connection) {
                return response()->json([
                    'success' => false,
                    'message' => 'No database connection configured for this project. Please set up a database connection first.'
                ], 400);
            }

            // Update connection settings if provided
            if ($request->has('autoSync') || $request->has('syncFrequency')) {
                $connection->update([
                    'auto_sync' => $request->autoSync ?? $connection->auto_sync,
                    'sync_frequency' => $request->syncFrequency ?? $connection->sync_frequency,
                ]);
            }

            // Create a knowledge source for the table
            $source = KnowledgeSource::create([
                'name' => "Table: {$tableName}",
                'type' => 'database',
                'description' => "Database table {$tableName}",
                'project_id' => $projectId,
                'status' => 'active',
            ]);

            // Log the action
            $this->auditLogService->logSourceAction(
                'create',
                $source->id,
                $request->user()->id ?? null,
                [
                    'table_name' => $tableName,
                    'project_id' => $projectId,
                    'auto_sync' => $request->autoSync ?? false,
                ]
            );

            // If embedAll is true, queue a job to generate embeddings
            if ($request->embedAll) {
                // Get the table data
                $tableData = $this->databaseService->executeReadOnlyQuery(
                    $projectId,
                    "SELECT * FROM {$tableName}",
                    1000 // Limit to 1000 rows for initial embedding
                );

                if (!isset($tableData['error']) && !empty($tableData['rows'])) {
                    // Queue the embedding generation job
                    dispatch(function () use ($source, $tableData, $request) {
                        foreach ($tableData['rows'] as $row) {
                            // Convert row to JSON for embedding
                            $content = json_encode($row);

                            // Create a document for this row
                            $document = Document::create([
                                'source_id' => $source->id,
                                'project_id' => $source->project_id,
                                'title' => "Table {$source->name} - Row Data",
                                'content' => $content,
                                'status' => 'ready',
                                'format' => 'json',
                                'size' => strlen($content),
                                'created_by' => $request->user()->id ?? null,
                            ]);

                            // Generate embeddings
                            $this->embeddingService->generateEmbeddings($document, $request->embedModel);
                        }
                    })->afterResponse();
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Table {$tableName} added to knowledge base",
                'source' => $source
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add table to knowledge base: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update a database connection.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveDatabaseConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'project_id' => 'required|exists:projects,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'driver' => 'required|string|in:mysql,pgsql,sqlsrv',
            'host' => 'required|string|max:255',
            'port' => 'required|integer',
            'database' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'password' => 'required|string',
            'auto_sync' => 'nullable|boolean',
            'sync_frequency' => 'nullable|in:hourly,daily,weekly,monthly',
            'id' => 'nullable|exists:database_connections,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            // Check authorization
            if ($request->user() && !$request->user()->can('create', [KnowledgeSource::class, $request->project_id])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Test the connection before saving
            $testConfig = [
                "database.connections.test_connection" => [
                    'driver' => $request->driver,
                    'host' => $request->host,
                    'port' => $request->port,
                    'database' => $request->database,
                    'username' => $request->username,
                    'password' => $request->password,
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                ],
            ];

            config($testConfig);

            try {
                DB::connection('test_connection')->getPdo();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Connection test failed: ' . $e->getMessage()
                ], 400);
            }

            // Create or update the connection
            $data = [
                'project_id' => $request->project_id,
                'name' => $request->name,
                'description' => $request->description,
                'driver' => $request->driver,
                'host' => $request->host,
                'port' => $request->port,
                'database' => $request->database,
                'username' => $request->username,
                'password' => $request->password,
                'status' => 'active',
                'auto_sync' => $request->auto_sync ?? false,
                'sync_frequency' => $request->sync_frequency ?? 'daily',
            ];

            if ($request->id) {
                $connection = DatabaseConnection::findOrFail($request->id);
                $connection->update($data);
                $message = 'Database connection updated successfully';
            } else {
                $connection = DatabaseConnection::create($data);
                $message = 'Database connection created successfully';
            }

            // Log the action
            $this->auditLogService->logDatabaseAction(
                $request->id ? 'update' : 'create',
                $connection->id,
                $request->user()->id ?? null,
                [
                    'name' => $connection->name,
                    'project_id' => $connection->project_id,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => $message,
                'connection' => $connection
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save database connection: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get database connections for a project.
     *
     * @param Request $request
     * @param int $projectId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDatabaseConnections(Request $request, $projectId)
    {
        try {
            // Check authorization
            if ($request->user() && !$request->user()->can('view', [KnowledgeSource::class, $projectId])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $connections = DatabaseConnection::where('project_id', $projectId)
                ->select(['id', 'name', 'description', 'driver', 'host', 'port', 'database', 'username', 'status', 'auto_sync', 'sync_frequency', 'last_sync_at', 'created_at', 'updated_at'])
                ->get();

            return response()->json([
                'success' => true,
                'data' => $connections
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get database connections: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a database connection.
     *
     * @param Request $request
     * @param int $connectionId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteDatabaseConnection(Request $request, $connectionId)
    {
        try {
            $connection = DatabaseConnection::findOrFail($connectionId);

            // Check authorization
            if ($request->user() && !$request->user()->can('delete', [KnowledgeSource::class, $connection->project_id])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            // Log the action before deleting
            $this->auditLogService->logDatabaseAction(
                'delete',
                $connection->id,
                $request->user()->id ?? null,
                [
                    'name' => $connection->name,
                    'project_id' => $connection->project_id,
                ]
            );

            $connection->delete();

            return response()->json([
                'success' => true,
                'message' => 'Database connection deleted successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete database connection: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test a database connection.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testDatabaseConnection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'driver' => 'required|string|in:mysql,pgsql,sqlsrv',
            'host' => 'required|string|max:255',
            'port' => 'required|integer',
            'database' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        try {
            // Test the connection
            $testConfig = [
                "database.connections.test_connection" => [
                    'driver' => $request->driver,
                    'host' => $request->host,
                    'port' => $request->port,
                    'database' => $request->database,
                    'username' => $request->username,
                    'password' => $request->password,
                    'charset' => 'utf8mb4',
                    'collation' => 'utf8mb4_unicode_ci',
                    'prefix' => '',
                ],
            ];

            config($testConfig);

            try {
                $pdo = DB::connection('test_connection')->getPdo();

                // Get database version
                $version = DB::connection('test_connection')->select('SELECT version()')[0]->{'version()'};

                return response()->json([
                    'success' => true,
                    'message' => 'Connection successful',
                    'version' => $version
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Connection test failed: ' . $e->getMessage()
                ], 400);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test database connection: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get project context settings
     *
     * @param Request $request
     * @param int $projectId
     * @return JsonResponse
     */
    public function getProjectContext(Request $request, $projectId): JsonResponse
    {
        try {
            $project = Project::findOrFail($projectId);

            // Get context settings for the project
            $contextSettings = ContextSetting::where('project_id', $projectId)
                ->first();

            // If settings don't exist, create default settings
            if (!$contextSettings) {
                $contextSettings = new ContextSetting([
                    'project_id' => $projectId,
                    'priority' => [
                        'documents' => 80,
                        'database' => 50,
                        'web' => 30
                    ],
                    'context_retention' => 'session',
                    'relevance_threshold' => 0.7,
                    'max_sources_per_query' => 5,
                    'enabled_sources' => [
                        'documents' => true,
                        'database' => true,
                        'web' => true
                    ]
                ]);
                $contextSettings->save();
            }

            // Log access for audit
            $this->auditLogService->log(
                'knowledge_base',
                'access',
                'Retrieved context settings for project ' . $project->name,
                $request->user()->id ?? null,
                $projectId
            );

            return response()->json([
                'success' => true,
                'data' => $contextSettings
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get project context settings: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get project context settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update project context settings
     *
     * @param Request $request
     * @param int $projectId
     * @return JsonResponse
     */
    public function updateProjectContext(Request $request, $projectId): JsonResponse
    {
        try {
            $project = Project::findOrFail($projectId);

            $validator = Validator::make($request->all(), [
                'priority' => 'nullable|array',
                'priority.documents' => 'nullable|integer|min:0|max:100',
                'priority.database' => 'nullable|integer|min:0|max:100',
                'priority.web' => 'nullable|integer|min:0|max:100',
                'context_retention' => 'nullable|string|in:none,message,conversation,session,user',
                'relevance_threshold' => 'nullable|numeric|min:0|max:1',
                'max_sources_per_query' => 'nullable|integer|min:1|max:20',
                'enabled_sources' => 'nullable|array',
                'enabled_sources.documents' => 'nullable|boolean',
                'enabled_sources.database' => 'nullable|boolean',
                'enabled_sources.web' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Get or create context settings
            $contextSettings = ContextSetting::where('project_id', $projectId)->first();
            if (!$contextSettings) {
                $contextSettings = new ContextSetting(['project_id' => $projectId]);
            }

            // Update fields
            if ($request->has('priority')) {
                $contextSettings->priority = $request->input('priority');
            }

            if ($request->has('context_retention')) {
                $contextSettings->context_retention = $request->input('context_retention');
            }

            if ($request->has('relevance_threshold')) {
                $contextSettings->relevance_threshold = $request->input('relevance_threshold');
            }

            if ($request->has('max_sources_per_query')) {
                $contextSettings->max_sources_per_query = $request->input('max_sources_per_query');
            }

            if ($request->has('enabled_sources')) {
                $contextSettings->enabled_sources = $request->input('enabled_sources');
            }

            $contextSettings->save();

            // Log update for audit
            $this->auditLogService->log(
                'knowledge_base',
                'update',
                'Updated context settings for project ' . $project->name,
                $request->user()->id ?? null,
                $projectId
            );

            return response()->json([
                'success' => true,
                'data' => $contextSettings,
                'message' => 'Context settings updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to update project context settings: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update project context settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get project context rules
     *
     * @param Request $request
     * @param int $projectId
     * @return JsonResponse
     */
    public function getProjectContextRules(Request $request, $projectId): JsonResponse
    {
        try {
            $project = Project::findOrFail($projectId);

            // Get context rules for the project
            $contextRules = ContextRule::where('project_id', $projectId)
                ->orderBy('priority', 'desc')
                ->get();

            // Log access for audit
            $this->auditLogService->log(
                'knowledge_base',
                'access',
                'Retrieved context rules for project ' . $project->name,
                $request->user()->id ?? null,
                $projectId
            );

            return response()->json([
                'success' => true,
                'data' => $contextRules
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get project context rules: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get project context rules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new project context rule
     *
     * @param Request $request
     * @param int $projectId
     * @return JsonResponse
     */
    public function createProjectContextRule(Request $request, $projectId): JsonResponse
    {
        try {
            $project = Project::findOrFail($projectId);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'sources' => 'required|array',
                'keywords' => 'required|array',
                'priority' => 'nullable|integer|min:0|max:100',
                'active' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            // Create new context rule
            $contextRule = new ContextRule([
                'project_id' => $projectId,
                'name' => $request->input('name'),
                'description' => $request->input('description'),
                'sources' => $request->input('sources'),
                'keywords' => $request->input('keywords'),
                'priority' => $request->input('priority', 50),
                'active' => $request->input('active', true),
            ]);

            $contextRule->save();

            // Log creation for audit
            $this->auditLogService->log(
                'knowledge_base',
                'create',
                'Created context rule ' . $contextRule->name . ' for project ' . $project->name,
                $request->user()->id ?? null,
                $projectId
            );

            return response()->json([
                'success' => true,
                'data' => $contextRule,
                'message' => 'Context rule created successfully'
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create project context rule: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to create project context rule: ' . $e->getMessage()
            ], 500);
        }
    }
}
