import React, { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Search,
  Plus,
  MoreVertical,
  Calendar,
  FileText,
  Database,
  Globe,
  Settings,
  Trash2,
  Edit,
  Eye,
  Brain
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'
import { ModernProjectDialog } from './modern-project-dialog'

interface Project {
  id: number
  name: string
  description?: string
  created_at: string
  documents_count?: number
  sources_count?: number
  embeddings_count?: number
  status?: string
}

interface ProjectStats {
  total_documents: number
  total_sources: number
  active_sources: number
  embeddings_generated: number
  last_updated: string
}

interface ModernProjectListingProps {
  onProjectSelect: (project: Project) => void
  selectedProjectId?: number
}

export function ModernProjectListing({ onProjectSelect, selectedProjectId }: ModernProjectListingProps) {
  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [projectStats, setProjectStats] = useState<Record<number, ProjectStats>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // Fetch projects
  useEffect(() => {
    const fetchProjects = async () => {
      setIsLoading(true)
      try {
        const response = await knowledgeBaseService.getProjects()
        if (response?.data?.success) {
          const projectsData = response.data.data || []
          setProjects(projectsData)
          setFilteredProjects(projectsData)

          // Fetch stats for each project
          const statsPromises = projectsData.map(async (project: Project) => {
            try {
              const statsResponse = await knowledgeBaseService.getProjectStats?.(project.id)
              if (statsResponse?.data?.success) {
                return { projectId: project.id, stats: statsResponse.data.data }
              }
            } catch (error) {
              console.error(`Failed to load stats for project ${project.id}:`, error)
            }
            return {
              projectId: project.id,
              stats: {
                total_documents: 0,
                total_sources: 0,
                active_sources: 0,
                embeddings_generated: 0,
                last_updated: new Date().toISOString()
              }
            }
          })

          const statsResults = await Promise.all(statsPromises)
          const statsMap: Record<number, ProjectStats> = {}
          statsResults.forEach(result => {
            if (result) {
              statsMap[result.projectId] = result.stats
            }
          })
          setProjectStats(statsMap)
        }
      } catch (error) {
        console.error('Failed to load projects:', error)
        toast.error('Failed to load projects')
      } finally {
        setIsLoading(false)
      }
    }

    fetchProjects()
  }, [])

  // Filter projects based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredProjects(projects)
    } else {
      const filtered = projects.filter(project =>
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredProjects(filtered)
    }
  }, [searchQuery, projects])

  const handleProjectCreated = (newProject: Project) => {
    setProjects(prev => [...prev, newProject])
    // Add default stats for the new project
    setProjectStats(prev => ({
      ...prev,
      [newProject.id]: {
        total_documents: 0,
        total_sources: 0,
        active_sources: 0,
        embeddings_generated: 0,
        last_updated: new Date().toISOString()
      }
    }))
    setShowCreateDialog(false)
    toast.success(`Project "${newProject.name}" created successfully!`)
  }

  const handleDeleteProject = async (projectId: number) => {
    if (!confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      return
    }

    try {
      // Add delete API call here when implemented
      toast.success('Project deleted successfully')
      setProjects(prev => prev.filter(p => p.id !== projectId))
    } catch (error) {
      toast.error('Failed to delete project')
    }
  }

  const getProjectStatusBadge = (project: Project) => {
    const stats = projectStats[project.id]
    if (!stats) {
      return <Badge variant="secondary">Loading...</Badge>
    }

    const hasDocuments = stats.total_documents > 0
    const hasSources = stats.total_sources > 0
    const hasEmbeddings = stats.embeddings_generated > 0

    if (hasEmbeddings) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Ready</Badge>
    } else if (hasDocuments || hasSources) {
      return <Badge variant="default" className="bg-yellow-100 text-yellow-800">Processing</Badge>
    }
    return <Badge variant="secondary">Empty</Badge>
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading projects...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
                <Brain className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle>Knowledge Base Projects</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Manage your AI knowledge bases
                </p>
              </div>
            </div>

            <Button
              onClick={() => setShowCreateDialog(true)}
              className="bg-gradient-to-r from-primary to-primary/80"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="outline" className="px-3 py-1">
              {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Projects Grid */}
      {filteredProjects.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchQuery ? 'No projects found' : 'No projects yet'}
            </h3>
            <p className="text-muted-foreground mb-6">
              {searchQuery
                ? 'Try adjusting your search terms'
                : 'Create your first knowledge base project to get started'
              }
            </p>
            {!searchQuery && (
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Project
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <Card
              key={project.id}
              className={`cursor-pointer transition-all hover:shadow-md hover:border-primary/30 ${selectedProjectId === project.id ? 'ring-2 ring-primary/20 bg-primary/5' : ''
                }`}
              onClick={() => onProjectSelect(project)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-1">{project.name}</CardTitle>
                    {project.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {project.description}
                      </p>
                    )}
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation()
                        onProjectSelect(project)
                      }}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Project
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                        <Settings className="h-4 w-4 mr-2" />
                        Settings
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteProject(project.id)
                        }}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-3">
                  {/* Status Badge */}
                  <div className="flex items-center justify-between">
                    {getProjectStatusBadge(project)}
                    <span className="text-xs text-muted-foreground">
                      {new Date(project.created_at).toLocaleDateString()}
                    </span>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="p-2 bg-muted/30 rounded-lg">
                      <FileText className="h-4 w-4 text-blue-600 mx-auto mb-1" />
                      <p className="text-xs font-medium">{projectStats[project.id]?.total_documents || 0}</p>
                      <p className="text-xs text-muted-foreground">Docs</p>
                    </div>
                    <div className="p-2 bg-muted/30 rounded-lg">
                      <Database className="h-4 w-4 text-green-600 mx-auto mb-1" />
                      <p className="text-xs font-medium">{projectStats[project.id]?.total_sources || 0}</p>
                      <p className="text-xs text-muted-foreground">Sources</p>
                    </div>
                    <div className="p-2 bg-muted/30 rounded-lg">
                      <Brain className="h-4 w-4 text-purple-600 mx-auto mb-1" />
                      <p className="text-xs font-medium">{projectStats[project.id]?.embeddings_generated || 0}</p>
                      <p className="text-xs text-muted-foreground">AI Ready</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Project Dialog */}
      <ModernProjectDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  )
}
