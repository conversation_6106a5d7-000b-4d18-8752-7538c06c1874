import React, { useState, useCallback, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  X,
  Zap,
  Eye,
  Trash2
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'

interface ModernDocumentManagerProps {
  projectId: number
  onDocumentUploaded?: () => void
}

interface UploadingFile {
  id: string
  file: File
  progress: number
  status: 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
}

export function ModernDocumentManager({ projectId, onDocumentUploaded }: ModernDocumentManagerProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const supportedFormats = [
    'PDF', 'DOCX', 'DOC', 'XLSX', 'XLS', 'CSV', 
    'TXT', 'JSON', 'HTML', 'HTM', 'MD', 'MARKDOWN'
  ]

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return

    Array.from(files).forEach(file => {
      // Validate file type
      const fileExtension = file.name.split('.').pop()?.toLowerCase()
      if (!fileExtension || !supportedFormats.map(f => f.toLowerCase()).includes(fileExtension)) {
        toast.error(`Unsupported file format: ${file.name}`)
        return
      }

      // Validate file size (max 100MB)
      const maxSize = 100 * 1024 * 1024
      if (file.size > maxSize) {
        toast.error(`File too large: ${file.name} (max 100MB)`)
        return
      }

      const uploadingFile: UploadingFile = {
        id: Math.random().toString(36).substr(2, 9),
        file,
        progress: 0,
        status: 'uploading'
      }

      setUploadingFiles(prev => [...prev, uploadingFile])
      uploadFile(uploadingFile)
    })
  }, [projectId])

  const uploadFile = async (uploadingFile: UploadingFile) => {
    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadingFiles(prev => prev.map(f => 
          f.id === uploadingFile.id 
            ? { ...f, progress: Math.min(f.progress + 10, 90) }
            : f
        ))
      }, 200)

      const response = await knowledgeBaseService.uploadDocument(uploadingFile.file, {
        project_id: projectId,
        category: 'general',
        is_active_source: true
      })

      clearInterval(progressInterval)

      if (response?.data?.success) {
        setUploadingFiles(prev => prev.map(f => 
          f.id === uploadingFile.id 
            ? { ...f, progress: 100, status: 'processing' }
            : f
        ))

        // Simulate processing time
        setTimeout(() => {
          setUploadingFiles(prev => prev.map(f => 
            f.id === uploadingFile.id 
              ? { ...f, status: 'completed' }
              : f
          ))

          toast.success(`${uploadingFile.file.name} uploaded successfully!`)
          onDocumentUploaded?.()

          // Remove from list after 3 seconds
          setTimeout(() => {
            setUploadingFiles(prev => prev.filter(f => f.id !== uploadingFile.id))
          }, 3000)
        }, 2000)
      } else {
        throw new Error(response?.data?.message || 'Upload failed')
      }
    } catch (error) {
      clearInterval(progressInterval)
      setUploadingFiles(prev => prev.map(f => 
        f.id === uploadingFile.id 
          ? { ...f, status: 'error', error: error.message }
          : f
      ))
      toast.error(`Failed to upload ${uploadingFile.file.name}`)
    }
  }

  const removeUploadingFile = (id: string) => {
    setUploadingFiles(prev => prev.filter(f => f.id !== id))
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const getStatusIcon = (status: UploadingFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (status: UploadingFile['status']) => {
    switch (status) {
      case 'uploading':
        return 'Uploading...'
      case 'processing':
        return 'Processing...'
      case 'completed':
        return 'Ready for AI'
      case 'error':
        return 'Upload failed'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload Documents
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Upload Area */}
        <div
          className={`
            border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
            ${isDragOver 
              ? 'border-primary bg-primary/5' 
              : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-muted/50'
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            accept=".pdf,.docx,.doc,.xlsx,.xls,.csv,.txt,.json,.html,.htm,.md,.markdown"
            onChange={(e) => handleFileSelect(e.target.files)}
          />
          
          <div className="space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Upload className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Drop files here or click to browse</h3>
              <p className="text-muted-foreground">
                Upload documents to teach your AI assistant
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-1">
              {supportedFormats.slice(0, 6).map(format => (
                <Badge key={format} variant="outline" className="text-xs">
                  {format}
                </Badge>
              ))}
              <Badge variant="outline" className="text-xs">
                +{supportedFormats.length - 6} more
              </Badge>
            </div>
          </div>
        </div>

        {/* Upload Progress */}
        {uploadingFiles.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Upload Progress</h4>
            {uploadingFiles.map(file => (
              <div key={file.id} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-medium truncate">{file.file.name}</p>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(file.status)}
                      <span className="text-xs text-muted-foreground">
                        {getStatusText(file.status)}
                      </span>
                    </div>
                  </div>
                  {file.status === 'uploading' && (
                    <Progress value={file.progress} className="h-1" />
                  )}
                  {file.status === 'processing' && (
                    <Progress value={100} className="h-1" />
                  )}
                  {file.error && (
                    <p className="text-xs text-red-500 mt-1">{file.error}</p>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => removeUploadingFile(file.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-3">
          <Button 
            variant="outline" 
            className="flex-1"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-4 w-4 mr-2" />
            Choose Files
          </Button>
          <Button variant="outline" className="flex-1">
            <Zap className="h-4 w-4 mr-2" />
            Process All
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
