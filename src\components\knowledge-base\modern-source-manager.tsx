import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Globe, 
  Database, 
  Link, 
  Plus,
  CheckCircle,
  Clock,
  AlertCircle,
  Zap
} from 'lucide-react'
import { knowledgeBaseService } from '@/utils/knowledge-base-service'
import { toast } from 'sonner'

interface ModernSourceManagerProps {
  projectId: number
  onSourceAdded?: () => void
}

interface WebsiteSource {
  url: string
  status: 'idle' | 'scraping' | 'completed' | 'error'
  title?: string
  error?: string
}

export function ModernSourceManager({ projectId, onSourceAdded }: ModernSourceManagerProps) {
  const [activeTab, setActiveTab] = useState('website')
  const [websiteUrl, setWebsiteUrl] = useState('')
  const [isScrapingWebsite, setIsScrapingWebsite] = useState(false)
  const [recentSources, setRecentSources] = useState<WebsiteSource[]>([])

  const handleScrapeWebsite = async () => {
    if (!websiteUrl.trim()) {
      toast.error('Please enter a website URL')
      return
    }

    // Validate URL
    try {
      new URL(websiteUrl)
    } catch {
      toast.error('Please enter a valid URL')
      return
    }

    setIsScrapingWebsite(true)
    
    const newSource: WebsiteSource = {
      url: websiteUrl,
      status: 'scraping'
    }
    
    setRecentSources(prev => [newSource, ...prev.slice(0, 4)])

    try {
      const response = await knowledgeBaseService.scrapeUrl(websiteUrl, {
        project_id: projectId,
        table_name: 'scraped_content',
        render_js: true,
        use_cache: false
      })

      if (response?.data?.success) {
        // Update the source status
        setRecentSources(prev => prev.map(source => 
          source.url === websiteUrl 
            ? { ...source, status: 'completed', title: response.data.data?.title }
            : source
        ))

        toast.success('Website content imported successfully!')
        setWebsiteUrl('')
        onSourceAdded?.()
      } else {
        throw new Error(response?.data?.message || 'Failed to scrape website')
      }
    } catch (error) {
      setRecentSources(prev => prev.map(source => 
        source.url === websiteUrl 
          ? { ...source, status: 'error', error: error.message }
          : source
      ))
      toast.error('Failed to import website content')
    } finally {
      setIsScrapingWebsite(false)
    }
  }

  const getStatusIcon = (status: WebsiteSource['status']) => {
    switch (status) {
      case 'scraping':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />
    }
  }

  const getStatusText = (status: WebsiteSource['status']) => {
    switch (status) {
      case 'scraping':
        return 'Importing...'
      case 'completed':
        return 'Ready'
      case 'error':
        return 'Failed'
      default:
        return 'Pending'
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link className="h-5 w-5" />
          Data Sources
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="website" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Website
            </TabsTrigger>
            <TabsTrigger value="database" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Database
            </TabsTrigger>
          </TabsList>

          <TabsContent value="website" className="space-y-6 mt-6">
            {/* Website Import */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="website-url">Website URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="website-url"
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    placeholder="https://example.com"
                    className="flex-1"
                    disabled={isScrapingWebsite}
                  />
                  <Button 
                    onClick={handleScrapeWebsite}
                    disabled={isScrapingWebsite || !websiteUrl.trim()}
                  >
                    {isScrapingWebsite ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    ) : (
                      <Plus className="h-4 w-4 mr-2" />
                    )}
                    Import
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start gap-3">
                  <Globe className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">How it works</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      We'll extract text content from the webpage and make it available to your AI assistant. 
                      This includes articles, product descriptions, FAQs, and other text content.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Sources */}
            {recentSources.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium">Recent Imports</h4>
                {recentSources.map((source, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
                    <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {source.title || source.url}
                      </p>
                      <p className="text-xs text-muted-foreground truncate">
                        {source.url}
                      </p>
                      {source.error && (
                        <p className="text-xs text-red-500 mt-1">{source.error}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(source.status)}
                      <Badge 
                        variant={source.status === 'completed' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {getStatusText(source.status)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="database" className="space-y-6 mt-6">
            {/* Database Connection */}
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Database className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Connect Your Database</h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Link to your existing databases to make structured data available to your AI assistant
              </p>
              
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto mb-6">
                <div className="p-4 border rounded-lg">
                  <Database className="h-6 w-6 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium">MySQL</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <Database className="h-6 w-6 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium">PostgreSQL</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <Database className="h-6 w-6 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium">SQLite</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <Database className="h-6 w-6 text-primary mx-auto mb-2" />
                  <p className="text-sm font-medium">MongoDB</p>
                </div>
              </div>

              <Button className="bg-gradient-to-r from-primary to-primary/80">
                <Plus className="h-4 w-4 mr-2" />
                Connect Database
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Actions */}
        <div className="flex gap-3 mt-6 pt-6 border-t">
          <Button variant="outline" className="flex-1">
            <Zap className="h-4 w-4 mr-2" />
            Auto-Sync
          </Button>
          <Button variant="outline" className="flex-1">
            <Clock className="h-4 w-4 mr-2" />
            Schedule Updates
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
