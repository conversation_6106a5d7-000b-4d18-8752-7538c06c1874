import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  Brain, 
  Zap,
  Shield,
  Clock,
  Database,
  Save,
  RotateCcw
} from 'lucide-react'
import { toast } from 'sonner'

interface ModernSettingsManagerProps {
  projectId: number
  projectName: string
}

interface ProjectSettings {
  ai_processing: {
    auto_generate_embeddings: boolean
    embedding_model: string
    chunk_size: number
    chunk_overlap: number
  }
  content_filtering: {
    enable_content_filter: boolean
    filter_sensitive_content: boolean
    custom_filters: string[]
  }
  performance: {
    max_documents: number
    cache_duration: number
    batch_processing: boolean
  }
  security: {
    require_authentication: boolean
    enable_audit_log: boolean
    data_retention_days: number
  }
}

export function ModernSettingsManager({ projectId, projectName }: ModernSettingsManagerProps) {
  const [settings, setSettings] = useState<ProjectSettings>({
    ai_processing: {
      auto_generate_embeddings: true,
      embedding_model: 'openai-text-embedding-ada-002',
      chunk_size: 1000,
      chunk_overlap: 200
    },
    content_filtering: {
      enable_content_filter: false,
      filter_sensitive_content: true,
      custom_filters: []
    },
    performance: {
      max_documents: 1000,
      cache_duration: 24,
      batch_processing: true
    },
    security: {
      require_authentication: true,
      enable_audit_log: true,
      data_retention_days: 90
    }
  })

  const [isSaving, setIsSaving] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const updateSetting = (category: keyof ProjectSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Settings saved successfully!')
      setHasChanges(false)
    } catch (error) {
      toast.error('Failed to save settings')
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    // Reset to default values
    setSettings({
      ai_processing: {
        auto_generate_embeddings: true,
        embedding_model: 'openai-text-embedding-ada-002',
        chunk_size: 1000,
        chunk_overlap: 200
      },
      content_filtering: {
        enable_content_filter: false,
        filter_sensitive_content: true,
        custom_filters: []
      },
      performance: {
        max_documents: 1000,
        cache_duration: 24,
        batch_processing: true
      },
      security: {
        require_authentication: true,
        enable_audit_log: true,
        data_retention_days: 90
      }
    })
    setHasChanges(false)
    toast.info('Settings reset to defaults')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Settings className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle>Project Settings</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Configure {projectName} knowledge base settings
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {hasChanges && (
                <Badge variant="secondary" className="animate-pulse">
                  Unsaved changes
                </Badge>
              )}
              <Button variant="outline" onClick={handleReset}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button onClick={handleSave} disabled={!hasChanges || isSaving}>
                {isSaving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save Changes
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* AI Processing Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            AI Processing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">Auto-generate Embeddings</Label>
              <p className="text-sm text-muted-foreground">
                Automatically process new documents for AI search
              </p>
            </div>
            <Switch
              checked={settings.ai_processing.auto_generate_embeddings}
              onCheckedChange={(checked) => updateSetting('ai_processing', 'auto_generate_embeddings', checked)}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <Label className="text-base font-medium">Chunk Size: {settings.ai_processing.chunk_size} characters</Label>
            <p className="text-sm text-muted-foreground">
              Size of text chunks for processing (larger = more context, smaller = more precise)
            </p>
            <Slider
              value={[settings.ai_processing.chunk_size]}
              onValueChange={([value]) => updateSetting('ai_processing', 'chunk_size', value)}
              max={2000}
              min={500}
              step={100}
              className="w-full"
            />
          </div>

          <div className="space-y-3">
            <Label className="text-base font-medium">Chunk Overlap: {settings.ai_processing.chunk_overlap} characters</Label>
            <p className="text-sm text-muted-foreground">
              Overlap between chunks to maintain context continuity
            </p>
            <Slider
              value={[settings.ai_processing.chunk_overlap]}
              onValueChange={([value]) => updateSetting('ai_processing', 'chunk_overlap', value)}
              max={500}
              min={0}
              step={50}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Performance Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-primary" />
            Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">Batch Processing</Label>
              <p className="text-sm text-muted-foreground">
                Process multiple documents simultaneously for better performance
              </p>
            </div>
            <Switch
              checked={settings.performance.batch_processing}
              onCheckedChange={(checked) => updateSetting('performance', 'batch_processing', checked)}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <Label className="text-base font-medium">Cache Duration: {settings.performance.cache_duration} hours</Label>
            <p className="text-sm text-muted-foreground">
              How long to cache processed content for faster access
            </p>
            <Slider
              value={[settings.performance.cache_duration]}
              onValueChange={([value]) => updateSetting('performance', 'cache_duration', value)}
              max={168}
              min={1}
              step={1}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            Security & Privacy
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">Enable Audit Log</Label>
              <p className="text-sm text-muted-foreground">
                Track all access and modifications to your knowledge base
              </p>
            </div>
            <Switch
              checked={settings.security.enable_audit_log}
              onCheckedChange={(checked) => updateSetting('security', 'enable_audit_log', checked)}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <Label className="text-base font-medium">Data Retention: {settings.security.data_retention_days} days</Label>
            <p className="text-sm text-muted-foreground">
              How long to keep deleted content in backup before permanent removal
            </p>
            <Slider
              value={[settings.security.data_retention_days]}
              onValueChange={([value]) => updateSetting('security', 'data_retention_days', value)}
              max={365}
              min={7}
              step={7}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
